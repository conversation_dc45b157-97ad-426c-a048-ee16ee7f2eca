using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Diagnostics;
using OpenCvSharp;
using NLog;

namespace ImageAnalysisTool.Core.Processors
{
    /// <summary>
    /// 像素三元组 - 记录三张图像对应像素的信息
    /// </summary>
    public struct PixelTriple
    {
        /// <summary>
        /// 像素位置
        /// </summary>
        public System.Drawing.Point Position { get; set; }

        /// <summary>
        /// 原图灰度值
        /// </summary>
        public ushort OriginalValue { get; set; }

        /// <summary>
        /// 我的增强图灰度值
        /// </summary>
        public ushort MyEnhancedValue { get; set; }

        /// <summary>
        /// 目标图灰度值
        /// </summary>
        public ushort TargetValue { get; set; }

        /// <summary>
        /// 处理后的值
        /// </summary>
        public ushort ProcessedValue { get; set; }

        /// <summary>
        /// 原图到目标图的变化量
        /// </summary>
        public int OriginalToTargetChange => TargetValue - OriginalValue;

        /// <summary>
        /// 原图到我的增强图的变化量
        /// </summary>
        public int OriginalToMyEnhancedChange => MyEnhancedValue - OriginalValue;

        /// <summary>
        /// 目标图与我的增强图的差异
        /// </summary>
        public int TargetToMyEnhancedDifference => TargetValue - MyEnhancedValue;
    }

    /// <summary>
    /// 像素级图像处理器 - 核心处理逻辑
    /// </summary>
    public class PixelProcessor
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        /// <summary>
        /// 获取三图像素对应关系
        /// </summary>
        /// <param name="original">原图</param>
        /// <param name="myEnhanced">我的增强图</param>
        /// <param name="target">目标图</param>
        /// <param name="sampleRate">采样率 (0.01-1.0)，大图像建议使用较小值</param>
        /// <returns>像素三元组列表</returns>
        public List<PixelTriple> GetPixelTriples(Mat original, Mat myEnhanced, Mat target, double sampleRate = 0.1)
        {
            if (original == null || original.Empty())
                throw new ArgumentException("原图不能为空");

            var triples = new List<PixelTriple>();
            
            try
            {
                int rows = original.Rows;
                int cols = original.Cols;
                
                // 计算采样步长
                int totalPixels = rows * cols;
                int targetSampleCount = (int)(totalPixels * sampleRate);
                int step = Math.Max(1, (int)Math.Sqrt(totalPixels / (double)targetSampleCount));
                
                logger.Info($"开始获取像素三元组 - 图像尺寸: {cols}x{rows}, 采样步长: {step}, 预计采样数: {targetSampleCount:N0}");

                var stopwatch = Stopwatch.StartNew();

                for (int y = 0; y < rows; y += step)
                {
                    for (int x = 0; x < cols; x += step)
                    {
                        var triple = new PixelTriple
                        {
                            Position = new System.Drawing.Point(x, y),
                            OriginalValue = original.At<ushort>(y, x)
                        };

                        // 获取我的增强图像素值
                        if (myEnhanced != null && !myEnhanced.Empty())
                        {
                            triple.MyEnhancedValue = myEnhanced.At<ushort>(y, x);
                        }

                        // 获取目标图像素值
                        if (target != null && !target.Empty())
                        {
                            triple.TargetValue = target.At<ushort>(y, x);
                        }

                        triples.Add(triple);
                    }
                }

                stopwatch.Stop();
                logger.Info($"像素三元组获取完成 - 实际采样数: {triples.Count:N0}, 耗时: {stopwatch.ElapsedMilliseconds}ms");

                return triples;
            }
            catch (Exception ex)
            {
                logger.Error(ex, "获取像素三元组失败");
                throw;
            }
        }

        /// <summary>
        /// 应用处理规则到图像
        /// </summary>
        /// <param name="input">输入图像</param>
        /// <param name="rule">处理规则</param>
        /// <returns>处理后的图像</returns>
        public Mat ApplyProcessingRule(Mat input, ProcessingRule rule)
        {
            if (input == null || input.Empty())
                throw new ArgumentException("输入图像不能为空");

            if (rule?.Transform == null)
                throw new ArgumentException("处理规则或变换函数不能为空");

            try
            {
                var stopwatch = Stopwatch.StartNew();
                var result = input.Clone();
                
                int processedCount = 0;
                var changes = new List<int>();
                double beforeSum = 0, afterSum = 0;

                // 确定处理区域
                var region = rule.ApplyRegion ?? new Rectangle(0, 0, input.Cols, input.Rows);
                
                logger.Info($"开始应用处理规则: {rule.RuleName}, 区域: {region}");

                // 应用变换
                for (int y = region.Y; y < Math.Min(region.Bottom, input.Rows); y++)
                {
                    for (int x = region.X; x < Math.Min(region.Right, input.Cols); x++)
                    {
                        ushort originalValue = input.At<ushort>(y, x);
                        ushort newValue = rule.Transform(originalValue);
                        
                        result.Set(y, x, newValue);
                        
                        // 统计信息
                        processedCount++;
                        int change = newValue - originalValue;
                        changes.Add(change);
                        beforeSum += originalValue;
                        afterSum += newValue;
                    }
                }

                stopwatch.Stop();

                // 更新统计信息
                rule.Statistics.ProcessedPixelCount = processedCount;
                rule.Statistics.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;
                rule.Statistics.BeforeAverageGray = beforeSum / processedCount;
                rule.Statistics.AfterAverageGray = afterSum / processedCount;
                rule.Statistics.AverageChange = changes.Average();
                rule.Statistics.MaxChange = changes.Max();
                rule.Statistics.MinChange = changes.Min();
                rule.Statistics.ChangeStandardDeviation = CalculateStandardDeviation(changes);

                logger.Info($"处理规则应用完成 - 处理像素数: {processedCount:N0}, 耗时: {stopwatch.ElapsedMilliseconds}ms");

                return result;
            }
            catch (Exception ex)
            {
                logger.Error(ex, $"应用处理规则失败: {rule.RuleName}");
                throw;
            }
        }

        /// <summary>
        /// 分析像素映射规律
        /// </summary>
        /// <param name="pixels">像素三元组列表</param>
        /// <returns>映射字典 (原图值 -> 目标图值)</returns>
        public Dictionary<int, int> AnalyzePixelMapping(List<PixelTriple> pixels)
        {
            if (pixels == null || pixels.Count == 0)
                return new Dictionary<int, int>();

            try
            {
                logger.Info($"开始分析像素映射规律 - 像素数: {pixels.Count:N0}");

                var mapping = new Dictionary<int, List<int>>();

                // 检测图像位深
                int maxOriginal = pixels.Max(p => p.OriginalValue);
                int maxTarget = pixels.Max(p => p.TargetValue);
                bool is16Bit = maxOriginal > 255 || maxTarget > 255;
                
                // 根据位深确定分组策略
                int binSize = is16Bit ? 64 : 1; // 16位图像分组，8位图像不分组
                
                logger.Debug($"检测到图像位深: {(is16Bit ? "16位" : "8位")}, 分组大小: {binSize}");

                // 分组统计
                foreach (var pixel in pixels)
                {
                    int origKey = is16Bit ? (pixel.OriginalValue / binSize) * binSize : pixel.OriginalValue;
                    int targetValue = pixel.TargetValue;

                    if (!mapping.ContainsKey(origKey))
                        mapping[origKey] = new List<int>();
                    
                    mapping[origKey].Add(targetValue);
                }

                // 计算每个区间的平均映射值
                var result = new Dictionary<int, int>();
                foreach (var kvp in mapping)
                {
                    if (kvp.Value.Count > 0)
                    {
                        result[kvp.Key] = (int)kvp.Value.Average();
                    }
                }

                logger.Info($"像素映射分析完成 - 映射点数: {result.Count}");
                return result;
            }
            catch (Exception ex)
            {
                logger.Error(ex, "分析像素映射规律失败");
                throw;
            }
        }

        /// <summary>
        /// 直接映射处理：将原图按照目标图的像素值进行映射
        /// </summary>
        /// <param name="original">原图</param>
        /// <param name="target">目标图</param>
        /// <param name="sampleRate">采样率</param>
        /// <returns>处理后的图像</returns>
        public Mat DirectMapping(Mat original, Mat target, double sampleRate = 0.1)
        {
            if (original == null || target == null)
                throw new ArgumentException("原图和目标图都不能为空");

            try
            {
                logger.Info("开始直接映射处理");

                // 获取像素对应关系
                var pixels = GetPixelTriples(original, null, target, sampleRate);
                
                // 分析映射规律
                var mapping = AnalyzePixelMapping(pixels);
                
                // 创建处理规则
                var rule = ProcessingRule.CreateDirectMapping("直接映射", mapping);
                
                // 应用处理规则
                return ApplyProcessingRule(original, rule);
            }
            catch (Exception ex)
            {
                logger.Error(ex, "直接映射处理失败");
                throw;
            }
        }

        /// <summary>
        /// 数学运算处理
        /// </summary>
        /// <param name="input">输入图像</param>
        /// <param name="operation">运算类型</param>
        /// <param name="value">运算值</param>
        /// <returns>处理后的图像</returns>
        public Mat MathOperation(Mat input, MathOperationType operation, double value)
        {
            if (input == null || input.Empty())
                throw new ArgumentException("输入图像不能为空");

            try
            {
                logger.Info($"开始数学运算处理: {operation} {value}");

                // 创建处理规则
                var rule = ProcessingRule.CreateMathOperation($"{operation}_{value}", operation, value);
                
                // 应用处理规则
                return ApplyProcessingRule(input, rule);
            }
            catch (Exception ex)
            {
                logger.Error(ex, $"数学运算处理失败: {operation} {value}");
                throw;
            }
        }

        /// <summary>
        /// 生成处理规律报告
        /// </summary>
        /// <param name="rules">处理规则列表</param>
        /// <returns>报告字符串</returns>
        public string GenerateProcessingReport(List<ProcessingRule> rules)
        {
            if (rules == null || rules.Count == 0)
                return "没有处理规则记录";

            try
            {
                var report = "=== 图像处理规律分析报告 ===\n\n";
                report += $"生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}\n";
                report += $"处理规则数量: {rules.Count}\n\n";

                for (int i = 0; i < rules.Count; i++)
                {
                    report += $"--- 规则 {i + 1} ---\n";
                    report += rules[i].GetDetailedInfo();
                    report += "\n";
                }

                // 添加总结分析
                report += "=== 总结分析 ===\n";
                report += AnalyzeProcessingPatterns(rules);

                return report;
            }
            catch (Exception ex)
            {
                logger.Error(ex, "生成处理规律报告失败");
                return $"生成报告失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 分析处理模式
        /// </summary>
        private string AnalyzeProcessingPatterns(List<ProcessingRule> rules)
        {
            var analysis = "";
            
            // 统计处理类型
            var typeStats = rules.GroupBy(r => r.Type)
                                 .ToDictionary(g => g.Key, g => g.Count());
            
            analysis += "处理类型统计:\n";
            foreach (var stat in typeStats)
            {
                analysis += $"  {stat.Key}: {stat.Value} 次\n";
            }

            // 分析平均变化量
            var avgChanges = rules.Where(r => r.Statistics != null)
                                 .Select(r => r.Statistics.AverageChange)
                                 .ToList();
            
            if (avgChanges.Count > 0)
            {
                analysis += $"\n平均变化量统计:\n";
                analysis += $"  最小: {avgChanges.Min():F2}\n";
                analysis += $"  最大: {avgChanges.Max():F2}\n";
                analysis += $"  平均: {avgChanges.Average():F2}\n";
            }

            return analysis;
        }

        /// <summary>
        /// 计算标准差
        /// </summary>
        private double CalculateStandardDeviation(List<int> values)
        {
            if (values.Count == 0) return 0;
            
            double mean = values.Average();
            double sumSquaredDifferences = values.Sum(v => Math.Pow(v - mean, 2));
            return Math.Sqrt(sumSquaredDifferences / values.Count);
        }
    }
}
