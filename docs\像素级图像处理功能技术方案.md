# 像素级图像处理功能技术方案

## 📋 项目概述

**功能名称**: 像素级图像处理工具  
**开发阶段**: Phase 1 - 基础框架实现  
**目标**: 通过像素级直接操作实现图像增强算法逆向工程  
**技术栈**: .NET 8.0 WinForms + OpenCvSharp4  

## 🎯 核心理念

传统的算法调参方式如"炼丹"般难以掌控，本功能采用最原始的像素级处理方式：
- 逐像素分析原图→目标图的变换规律
- 记录每个像素点的处理过程
- 通过多轮处理总结分析规律
- 最终实现精确的图像效果复现

## 🏗️ 项目结构调整

```
src/
├── Core/
│   ├── Analyzers/          # 现有分析器
│   ├── Enhancers/          # 现有增强器  
│   ├── Models/             # 现有数据模型
│   └── Processors/         # 新增：图像处理器模块
│       ├── PixelProcessor.cs           # 像素级处理核心
│       ├── ProcessingRule.cs           # 处理规则定义
│       ├── ProcessingHistory.cs        # 处理历史管理
│       └── IImageProcessor.cs          # 处理器接口(为Phase 2预留)
├── UI/
│   └── Forms/
│       ├── EnhancementAnalysisForm.cs  # 现有主窗口
│       └── ImageProcessingForm.cs      # 新增：图像处理窗口
└── Utils/                  # 现有工具类
```

## 📊 核心数据结构

### 像素三元组
```csharp
public struct PixelTriple 
{
    public Point Position;          // 像素位置(x,y)
    public ushort OriginalValue;    // 原图灰度值
    public ushort MyEnhancedValue;  // 我的增强图灰度值  
    public ushort TargetValue;      // 目标图灰度值
    public ushort ProcessedValue;   // 处理后的值
}
```

### 处理规则
```csharp
public class ProcessingRule
{
    public string RuleName;                    // 规则名称
    public ProcessingType Type;                // 处理类型
    public Func<ushort, ushort> Transform;     // 变换函数
    public Dictionary<string, object> Parameters; // 参数记录
    public DateTime CreateTime;                // 创建时间
    public string Description;                 // 规则描述
}

public enum ProcessingType
{
    DirectMapping,      // 直接映射 (原图值→目标图值)
    MathOperation,      // 数学运算 (加减乘除)
    LookupTable        // 查找表映射
}
```

## 🎨 UI设计方案

### 主窗口改动
在现有3列布局的顶部控制面板添加**"图像处理"**按钮，点击后弹出处理窗口。

### 处理窗口布局 (1200x800)
```
┌─────────────────────────────────────────────────────────┐
│ 工具栏：[保存] [撤销] [重做] [导出规律] [应用到原图]        │
├─────────────────┬───────────────────────────────────────┤
│                 │                                       │
│   图像预览区     │            处理控制面板                │
│   (左侧40%)     │            (右侧60%)                  │
│                 │                                       │
│ ┌─────────────┐ │ ┌─ 处理模式选择 ─────────────────────┐ │
│ │   原图       │ │ │ ● 像素级处理  ○ 区域处理  ○ 算法调参│ │
│ │             │ │ └─────────────────────────────────────┘ │
│ └─────────────┘ │                                       │
│ ┌─────────────┐ │ ┌─ 像素级处理工具 ─────────────────────┐ │
│ │   目标图     │ │ │ 处理方式：                           │ │
│ │             │ │ │ ○ 直接映射 (原图值→目标图值)          │ │
│ └─────────────┘ │ │ ○ 数学运算 (加减乘除)                │ │
│ ┌─────────────┐ │ │ ○ 查找表映射                        │ │
│ │   处理结果   │ │ │                                   │ │
│ │             │ │ │ 当前像素信息：                       │ │
│ └─────────────┘ │ │ 位置: (1234, 567)                  │ │
│                 │ │ 原图值: 12345                      │ │
│                 │ │ 目标值: 23456                      │ │
│                 │ │ 差值: +11111                       │ │
│                 │ │                                   │ │
│                 │ │ [开始逐像素处理] [批量应用规律]       │ │
│                 │ └─────────────────────────────────────┘ │
│                 │                                       │
│                 │ ┌─ 处理历史记录 ─────────────────────┐ │
│                 │ │ [操作步骤列表，可撤销/重做]         │ │
│                 │ │                                   │ │
│                 │ └─────────────────────────────────────┘ │
└─────────────────┴───────────────────────────────────────┘
```

## 🔧 核心功能模块

### PixelProcessor.cs - 像素处理核心
```csharp
public class PixelProcessor
{
    /// <summary>
    /// 获取三图像素对应关系
    /// </summary>
    public List<PixelTriple> GetPixelTriples(Mat original, Mat myEnhanced, Mat target);
    
    /// <summary>
    /// 应用处理规则到图像
    /// </summary>
    public Mat ApplyProcessingRule(Mat input, ProcessingRule rule);
    
    /// <summary>
    /// 分析像素映射规律
    /// </summary>
    public Dictionary<int, int> AnalyzePixelMapping(List<PixelTriple> pixels);
    
    /// <summary>
    /// 生成处理规律报告
    /// </summary>
    public string GenerateProcessingReport(List<ProcessingRule> rules);
    
    /// <summary>
    /// 直接映射处理：原图值→目标图值
    /// </summary>
    public Mat DirectMapping(Mat original, Mat target);
    
    /// <summary>
    /// 数学运算处理：加减乘除操作
    /// </summary>
    public Mat MathOperation(Mat input, string operation, double value);
}
```

### ProcessingHistory.cs - 处理历史管理
```csharp
public class ProcessingHistory
{
    private Stack<ProcessingRule> undoStack;
    private Stack<ProcessingRule> redoStack;
    
    /// <summary>
    /// 添加处理步骤
    /// </summary>
    public void AddStep(ProcessingRule rule);
    
    /// <summary>
    /// 撤销操作
    /// </summary>
    public ProcessingRule Undo();
    
    /// <summary>
    /// 重做操作
    /// </summary>
    public ProcessingRule Redo();
    
    /// <summary>
    /// 获取历史记录列表
    /// </summary>
    public List<ProcessingRule> GetHistory();
}
```

## 🚀 实现步骤

### Phase 1: 基础框架 (当前阶段)
1. **主窗口改动**: 
   - 在EnhancementAnalysisForm顶部添加"图像处理"按钮
   - 按钮点击事件打开ImageProcessingForm

2. **新建处理窗口**: 
   - 创建ImageProcessingForm.cs
   - 实现基本布局和UI控件
   - 图像显示和基本交互

3. **像素级处理核心**: 
   - 实现PixelProcessor.cs
   - 像素遍历和映射分析
   - 直接映射和数学运算功能

4. **处理历史机制**: 
   - 实现ProcessingHistory.cs
   - 撤销/重做功能
   - 操作步骤可视化

### Phase 2: 功能扩展 (预留框架)
1. **区域处理**: 选择工具和区域处理
2. **算法调参**: 集成现有增强算法  
3. **批量处理**: 多图像处理
4. **规律导出**: 处理规则的保存和加载

## ⚡ 关键技术点

### 像素遍历优化
- **采样策略**: 大图像(>2M像素)采用智能采样，避免内存溢出
- **ROI限制**: 支持限制处理范围到感兴趣区域
- **进度显示**: 实时显示处理进度和剩余时间
- **内存管理**: 及时释放OpenCV Mat对象

### 处理规律记录
- **操作记录**: 每个像素级操作都记录为ProcessingRule
- **规律分析**: 自动分析多次操作的共同规律
- **可视化对比**: 实时显示处理前后效果对比
- **规律导出**: 支持将发现的规律导出为可重用的处理模板

### 性能考虑
- **16位图像支持**: 完整支持0-65535灰度值范围
- **多线程处理**: 像素级操作使用并行处理
- **内存优化**: 大图像分块处理，控制内存使用
- **实时预览**: 小范围实时预览，大范围批量处理

## 📁 文件清单

### 新增文件
- `src/Core/Processors/PixelProcessor.cs` - 像素处理核心
- `src/Core/Processors/ProcessingRule.cs` - 处理规则定义  
- `src/Core/Processors/ProcessingHistory.cs` - 处理历史管理
- `src/Core/Processors/IImageProcessor.cs` - 处理器接口
- `src/UI/Forms/ImageProcessingForm.cs` - 图像处理窗口

### 修改文件
- `src/UI/Forms/EnhancementAnalysisForm.cs` - 添加"图像处理"按钮

## 🎯 预期效果

通过本功能，用户可以：
1. **精确复现**: 将原图处理成与目标图完全一致的效果
2. **规律发现**: 通过多次处理发现通用的像素变换规律
3. **算法逆向**: 基于像素级分析推断目标图使用的增强算法
4. **参数优化**: 为传统算法提供精确的参数调优指导

---

**文档版本**: v1.0  
**创建日期**: 2025-01-30  
**状态**: Phase 1 开发中
